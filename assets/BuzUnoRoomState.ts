// 简化的状态类，不继承 Schema 以避免循环引用问题
export class UserState {
  //当前用户状态
  state: number;
  //仅在state为[WATCHING]才有意义
  //观察哪个用户
  watchUid?: string;

  //准备状态
  static readonly READY = 0;
  //没有准备
  static readonly NOT_READY = 1;
  //正在游戏
  static readonly PLAYING = 2;
  //观察模式
  static readonly WATCHING = 2;

  constructor() {
    this.state = UserState.NOT_READY;
  }
}

export class Player {
  name: string;
  uid: string;
  avatar: string;
  stateWrapper: UserState;

  constructor(name: string, uid: string, avatar: string, state: UserState) {
    this.name = name;
    this.uid = uid;
    this.avatar = avatar;
    this.stateWrapper = state;
  }
}

export class UnoRoomState {
  userList: Player[] = [];
  currentTurn?: string;
}
