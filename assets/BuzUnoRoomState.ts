import { Schema, ArraySchema } from 'db://colyseus-sdk/colyseus.js';

export class UserState extends Schema {
  //当前用户状态
  @type("number") state: number;
  //仅在state为[WATCHING]才有意义
  //观察哪个用户
  @type("string") watchUid?: string;

  //准备状态
  static readonly READY = 0;
  //没有准备
  static readonly NOT_READY = 1;
  //正在游戏
  static readonly PLAYING = 2;
  //观察模式
  static readonly WATCHING = 2;

  constructor() {
    super();
    this.state = UserState.NOT_READY;
  }
}

export class Player extends Schema {
  @type("string") name: string;
  @type("string") uid: string;
  @type("string") avatar: string;
  @type(UserState) stateWrapper: UserState;

  constructor(name: string, uid: string, avatar: string, state: UserState) {
    super();
    this.name = name;
    this.uid = uid;
    this.avatar = avatar;
    this.stateWrapper = state;
  }
}

export class UnoRoomState extends Schema {
  @type([Player]) userList = new ArraySchema<Player>();
  @type("string") currentTurn?: string;
}
