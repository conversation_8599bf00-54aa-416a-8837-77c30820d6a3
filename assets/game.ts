import { _decorator, Component } from 'cc';
import { UnoRoomState } from './BuzUnoRoomState';
const { ccclass } = _decorator;
import Colyseus from 'db://colyseus-sdk/colyseus.js';

@ccclass('game')
export class game extends Component {
    start() {
        this.hello()
    }

    update(deltaTime: number) {

    }

    async hello() {
        let client = new Colyseus.Client("ws://localhost:2567");
        let room = await client.joinOrCreate("buz_uno_room", {})
        console.log('sync');

        // 监听房间状态变化
        room.onStateChange((state) => {
            console.log("Room state changed:", state);
        });

        // 监听消息
        room.onMessage("*", (type, message) => {
            console.log("Received message:", type, message);
        });
    }
}

