import { _decorator, Component, Node } from 'cc';
import { UnoRoomState } from './BuzUnoRoomState';
const { ccclass, property } = _decorator;
import Colyseus, { getStateCallbacks } from 'db://colyseus-sdk/colyseus.js';

@ccclass('game')
export class game extends Component {
    start() {
this.hello()
    }

    update(deltaTime: number) {
        
    }
    async hello() {
        let client = new Colyseus.Client("ws://localhost:2567");
        let room = await client.joinOrCreate<UnoRoomState>("buz_uno_room", {}, UnoRoomState)
        console.log('sync');
         
        // get state callbacks handler
        const $ = getStateCallbacks<UnoRoomState>(room);

        $(room.state).userList.onAdd((player, sessionId) => {
            console.log(player, "has been added at", sessionId);
        })
    }
}

