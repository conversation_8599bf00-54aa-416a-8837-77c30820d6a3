import {
    _decorator,
    Component,
    instantiate,
    Node,
    Prefab,
    input,
    Input,
    __private,
    tween,
    Vec3,
    Collider2D,
    Contact2DType,
    Animation,
    Layout,
    director,
    AudioClip,
    AudioSource
} from 'cc';
import { Client } from 'db://colyseus-sdk/colyseus';

const {ccclass, property} = _decorator;
// import type {UnoRoomState} from "../../BuzUnoRoomState"
@ccclass('game')
export class game extends Component {
    @property(Node)
    img_target: Node = null

    @property(Prefab)
    prefab_sword: Node = null

    @property(Node)
    sword_container: Node = null

    @property(Node)
    failure_panel: Node = null

    @property(AudioClip)
    audio_hit: AudioClip = null

    onLoad(): void {
        
       

       this.testColyseus()
       input.on(Input.EventType.TOUCH_START, this.onTouchStart, this)
    }
    async  testColyseus() {
        let client = new Client("ws://localhost:2567")
        // let  hello = await client.joinOrCreate<UnoRoomState>("buz_uno_room")
        // console.log(hello)
    }

    onTouchStart() {
        const sword = instantiate(this.prefab_sword);
        sword.setParent(this.sword_container)



        sword.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.BEGIN_CONTACT, this);
        tween(sword).to(.5, {position: new Vec3(0, -32, 0)}).call(() => {
            let worldPosition = sword.getWorldPosition();
            let worldScale = sword.getWorldScale(); // 获取世界缩放
            sword.setParent(this.img_target)
            sword.setWorldPosition(worldPosition)
            sword.setWorldScale(worldScale);
            sword.angle = -this.img_target.angle
            sword.getComponent(Collider2D).off(Contact2DType.BEGIN_CONTACT, this.BEGIN_CONTACT, this)


            let audioSource = this.node.getComponent(AudioSource);
            audioSource.clip = this.audio_hit
            audioSource.play()

        }).start()

    }

    BEGIN_CONTACT() {
        console.log("发送碰撞")
        this.failure_panel.active = true
        let component = this.img_target.getComponent(Animation);
        if (component) {
            // 调用 stop() 方法停止动画
            component.stop();
            console.log("动画已停止。");
        }
    }

    ONCLICK_FAILURE() {
        console.log("失败")
        director.loadScene("main")
    }

    start() {
        // const sword = instantiate(this.prefab_sword);
        // sword.setParent(this.sword_container)
    }

    update(deltaTime: number) {

    }
}

